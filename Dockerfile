FROM node:18-alpine

# Set timezone
ENV TZ=Asia/Kolkata

# Install tzdata for timezone support
RUN apk add --no-cache tzdata

# Create app directory
WORKDIR /usr/src/app

# Install app dependencies
# A wildcard is used to ensure both package.json AND package-lock.json are copied
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Bundle app source
COPY . .

# Expose the port the app runs on
EXPOSE 3003

# Command to run the application
CMD ["node", "server.js"]
