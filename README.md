# BTC API

A simple API for fetching and processing Bitcoin price data with MongoDB storage.

## Features

- Fetch BTC price data from Twelve Data API
- Store and retrieve candle data in MongoDB
- Clone TwelveData time_series API behavior
- Compare BTC with other assets
- Find patterns in price movements
- Cache results for better performance
- Parallel request handling for improved response times

## Installation

1. Clone the repository
2. Install dependencies:

```bash
npm install
```

3. Create a `.env` file based on `.env.example`:

```bash
cp .env.example .env
```

4. Edit the `.env` file and add your Twelve Data API key:

```
TWELVEDATA_API_KEY=your_api_key_here
```

## Configuration

The application can be configured using environment variables. These can be set in your `.env` file for local development or in the `docker-compose.yml` file for Docker deployment:

| Variable | Description | Default |
|----------|-------------|---------|
| `TWELVEDATA_API_KEY` | API key for Twelve Data | (required) |
| `MONGODB_URI` | MongoDB connection string | (required) |
| `MONGODB_DEBUG` | Enable MongoDB debug mode | false |
| `PORT` | Port for the server | 3001 |
| `NODE_ENV` | Environment (development/production) | development |
| `CACHE_TTL` | Cache time-to-live in seconds | 1200 |
| `CACHE_ENABLED` | Enable or disable caching | true |
| `MAX_PARALLEL_REQUESTS` | Maximum number of concurrent API requests | 5 |
| `DEFAULT_TIMEZONE` | Default timezone for API requests | Asia/Kolkata |

## Usage

### Running Locally

Start the server:

```bash
npm start
```

Or in development mode with auto-restart:

```bash
npm run dev
```

### Running with Docker

The application can be run using Docker and Docker Compose:

1. Make sure you have Docker and Docker Compose installed

2. Set your TwelveData API key in the environment or create a `.env` file:

```bash
TWELVEDATA_API_KEY=your_api_key_here
```

3. Use the comprehensive management script:

```bash
# Show available commands
./docker-manage.sh help

# Start the application
./docker-manage.sh start

# View logs
./docker-manage.sh logs

# Check status
./docker-manage.sh status

# Stop the application
./docker-manage.sh stop

# Restart the application
./docker-manage.sh restart

# Clean up everything
./docker-manage.sh clean
```

4. Alternatively, you can use the individual scripts or docker-compose directly:

```bash
# Start containers
./docker-start.sh
# or: docker-compose up -d

# View logs
docker-compose logs -f

# Stop containers
./docker-stop.sh
# or: docker-compose down
```

The application will be available at http://localhost:3003 and MongoDB will be accessible at mongodb://localhost:27017.

Both the Node.js application and MongoDB containers are configured to use the Asia/Kolkata (IST) timezone.

### API Endpoints

#### GET /data-live/btc

Fetch BTC data and find patterns.

**Parameters:**

- `roundCoefficent` - Coefficient for rounding (e.g., 0.1)
- `listRange` - Range of data points to process (e.g., 10)
- `indexDate` - Reference date as Unix timestamp
- `compare` - Comparison mode ('true' or 'false')
- `resolution` - Time resolution (e.g., '1d', '1h')
- `m1` - Primary symbol code (default: 'BTC')
- `m2` - Secondary symbol code (optional)

**Example:**

```
GET /data-live/btc?roundCoefficent=0.1&listRange=10&indexDate=1713110400&compare=true&resolution=1d&m1=BTC
```

#### POST /fetch-and-store

Fetch data from TwelveData API and store in MongoDB.

**Request Body:**

```json
{
  "symbol": "BTC/USD",
  "interval": "15min",
  "start_date": "2021-05-01",  // Optional
  "end_date": "2021-05-15"     // Optional
}
```

**Response:**

```json
{
  "success": true,
  "message": "Successfully fetched and stored candle data for BTC/USD (15min)",
  "symbol": "BTC/USD",
  "interval": "15min",
  "newCandlesStored": 42,
  "processingTimeMs": 1234
}
```

#### GET /time_series

Get time series data from MongoDB in TwelveData API format.

**Parameters:**

- `symbol` - Trading symbol (e.g., "BTC/USD")
- `interval` - Time interval (e.g., "15min", "1h", "1d")
- `start_date` - Start date (optional, format: "YYYY-MM-DD" or "YYYY-MM-DD HH:MM:SS")
- `end_date` - End date (optional, format: "YYYY-MM-DD" or "YYYY-MM-DD HH:MM:SS")
- `outputsize` - Number of candles to return (optional, default: 30)

**Note on query behavior:**
- When only `start_date` is provided (no `end_date`): Returns the first `outputsize` candles that occur on or after the `start_date`
- When both `start_date` and `end_date` are provided: Returns the most recent `outputsize` candles within the date range
- When only `end_date` is provided: Returns the most recent `outputsize` candles up to the `end_date`
- When neither is provided: Returns the most recent `outputsize` candles

All responses are returned in descending order (newest first) regardless of query parameters.

**Example:**

```
GET /time_series?symbol=BTC/USD&interval=15min&start_date=2021-05-01&end_date=2021-05-15&outputsize=5
```

**Response:**

```json
{
  "meta": {
    "symbol": "BTC/USD",
    "interval": "15min",
    "currency_base": "Bitcoin",
    "currency_quote": "US Dollar",
    "exchange": "Coinbase Pro",
    "type": "Digital Currency"
  },
  "values": [
    {
      "datetime": "2021-05-15 23:45:00",
      "open": "46750.011719",
      "high": "47112.26953",
      "low": "46645.17188",
      "close": "46738.80859"
    },
    // More candles...
  ],
  "status": "ok"
}
```

## Security

This application uses environment variables to securely manage API keys and other sensitive information. Never commit your `.env` file to version control.

## License

ISC
