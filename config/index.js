/**
 * Application configuration
 * Loads and validates environment variables
 */

// Validate required environment variables
const requiredEnvVars = ['TWELVEDATA_API_KEY', 'MONGODB_URI'];
const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  console.error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
  console.error('Please check your .env file or environment configuration');
  process.exit(1);
}

// Configuration object
const config = {
  // Server settings
  server: {
    port: parseInt(process.env.PORT) || 3001,
    nodeEnv: process.env.NODE_ENV || 'development',
    isDev: (process.env.NODE_ENV || 'development') === 'development'
  },

  // API keys
  api: {
    twelveDataApiKey: process.env.TWELVEDATA_API_KEY
  },

  // MongoDB settings
  mongodb: {
    uri: process.env.MONGODB_URI,
    debug: process.env.MONGODB_DEBUG === 'true',
    options: {
      // No need to specify deprecated options
    }
  },

  // Analysis database settings (separate database for analysis results)
  analysisDb: {
    uri: process.env.ANALYSIS_DB_URI || process.env.MONGODB_URI,
    dbName: 'gor_metrics',
    debug: process.env.MONGODB_DEBUG === 'true'
  },

  // Cache settings
  cache: {
    ttl: parseInt(process.env.CACHE_TTL) || 1200,
    prefix: 'btcapidata',
    enabled: process.env.CACHE_ENABLED !== 'false' // Default to true unless explicitly set to 'false'
  },

  // Concurrency settings
  concurrency: {
    maxParallelRequests: parseInt(process.env.MAX_PARALLEL_REQUESTS) || 5 // Default to 5 concurrent requests
  },

  // Timezone settings
  timezone: {
    default: process.env.DEFAULT_TIMEZONE || 'Asia/Kolkata'
  }
};

module.exports = config;
