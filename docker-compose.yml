version: '3.8'

services:
  app:
    build: .
    restart: unless-stopped
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongodb:27017/twelvedata
      - TWELVEDATA_API_KEY=${TWELVEDATA_API_KEY}
      - PORT=3003
      - DEFAULT_TIMEZONE=Asia/Kolkata
      - CACHE_ENABLED=true
      - CACHE_TTL=1200
      - MAX_PARALLEL_REQUESTS=10
      - TZ=Asia/Kolkata
    depends_on:
      - mongodb
    volumes:
      - ./:/usr/src/app
      - /usr/src/app/node_modules
    networks:
      - app-network

  mongodb:
    image: mongo:6
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      - TZ=Asia/Kolkata
    volumes:
      - mongodb-data:/data/db
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  mongodb-data:
    driver: local
