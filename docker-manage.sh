#!/bin/bash

# Colors for better readability
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
  echo -e "${RED}Error: Docker is not installed. Please install Docker first.${NC}"
  exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
  echo -e "${RED}Error: Docker Compose is not installed. Please install Docker Compose first.${NC}"
  exit 1
fi

# Function to display usage information
show_usage() {
  echo -e "${BLUE}Docker Management Script for TwelveData API Clone${NC}"
  echo ""
  echo "Usage: $0 [command]"
  echo ""
  echo "Commands:"
  echo "  start       - Build and start the containers"
  echo "  stop        - Stop and remove the containers"
  echo "  restart     - Restart the containers"
  echo "  logs        - Show logs from all containers"
  echo "  status      - Show the status of all containers"
  echo "  clean       - Remove all containers, networks, and volumes"
  echo "  help        - Show this help message"
  echo ""
  echo "Examples:"
  echo "  $0 start    - Start the application"
  echo "  $0 logs     - Show logs from all containers"
}

# Function to start containers
start_containers() {
  echo -e "${BLUE}Starting containers...${NC}"
  
  # Check if .env file exists
  if [ ! -f .env ]; then
    echo -e "${YELLOW}Warning: .env file not found. Creating a template...${NC}"
    echo "TWELVEDATA_API_KEY=your_api_key_here" > .env
    echo -e "${YELLOW}Please edit the .env file and add your TwelveData API key.${NC}"
    exit 1
  fi
  
  # Build and start the containers
  docker-compose up -d --build
  
  # Check if containers are running
  if [ $? -eq 0 ]; then
    echo -e "${GREEN}Containers started successfully!${NC}"
    echo -e "The application is now running at ${BLUE}http://localhost:3003${NC}"
    echo -e "MongoDB is accessible at ${BLUE}mongodb://localhost:27017${NC}"
  else
    echo -e "${RED}Error starting containers. Please check the logs with: docker-compose logs${NC}"
  fi
}

# Function to stop containers
stop_containers() {
  echo -e "${BLUE}Stopping containers...${NC}"
  
  # Stop the containers
  docker-compose down
  
  # Check if containers were stopped successfully
  if [ $? -eq 0 ]; then
    echo -e "${GREEN}Containers stopped successfully!${NC}"
  else
    echo -e "${RED}Error stopping containers. Please check if Docker is running properly.${NC}"
  fi
}

# Function to show logs
show_logs() {
  echo -e "${BLUE}Showing logs from all containers (press Ctrl+C to exit)...${NC}"
  docker-compose logs -f
}

# Function to show status
show_status() {
  echo -e "${BLUE}Container Status:${NC}"
  docker-compose ps
  
  echo -e "\n${BLUE}Resource Usage:${NC}"
  docker stats --no-stream $(docker-compose ps -q)
}

# Function to clean everything
clean_all() {
  echo -e "${YELLOW}Warning: This will remove all containers, networks, and volumes related to this project.${NC}"
  read -p "Are you sure you want to continue? (y/n) " -n 1 -r
  echo ""
  
  if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}Cleaning up...${NC}"
    
    # Stop and remove containers, networks, and volumes
    docker-compose down -v
    
    # Remove any dangling images
    docker image prune -f
    
    echo -e "${GREEN}Cleanup completed!${NC}"
  else
    echo -e "${BLUE}Cleanup cancelled.${NC}"
  fi
}

# Main script logic
case "$1" in
  start)
    start_containers
    ;;
  stop)
    stop_containers
    ;;
  restart)
    stop_containers
    start_containers
    ;;
  logs)
    show_logs
    ;;
  status)
    show_status
    ;;
  clean)
    clean_all
    ;;
  help|--help|-h)
    show_usage
    ;;
  *)
    echo -e "${RED}Error: Unknown command '$1'${NC}"
    show_usage
    exit 1
    ;;
esac

exit 0
