#!/bin/bash

# Check if .env file exists
if [ ! -f .env ]; then
  echo "Error: .env file not found. Please create one with your TWELVEDATA_API_KEY."
  echo "Example: TWELVEDATA_API_KEY=your_api_key_here"
  exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
  echo "Error: Docker is not installed. Please install Docker first."
  exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
  echo "Error: Docker Compose is not installed. Please install Docker Compose first."
  exit 1
fi

# Build and start the containers
echo "Building and starting containers..."
docker-compose up -d --build

# Check if containers are running
if [ $? -eq 0 ]; then
  echo "Containers started successfully!"
  echo "The application is now running at http://localhost:3003"
  echo "MongoDB is accessible at mongodb://localhost:27017"
  echo ""
  echo "To view logs: docker-compose logs -f"
  echo "To stop: docker-compose down"
else
  echo "Error starting containers. Please check the logs with: docker-compose logs"
fi
