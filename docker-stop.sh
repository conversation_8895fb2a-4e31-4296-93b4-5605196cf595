#!/bin/bash

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
  echo "Error: Docker is not installed. Please install Docker first."
  exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
  echo "Error: Docker Compose is not installed. Please install Docker Compose first."
  exit 1
fi

echo "Stopping Docker containers..."

# Stop the containers
docker-compose down

# Check if containers were stopped successfully
if [ $? -eq 0 ]; then
  echo "Containers stopped successfully!"
else
  echo "Error stopping containers. Please check if Dock<PERSON> is running properly."
fi

# Optional: List any remaining containers related to this project
REMAINING=$(docker ps -a --filter "name=backend-node-ai" --format "{{.Names}}")
if [ ! -z "$REMAINING" ]; then
  echo ""
  echo "Note: The following containers related to this project are still present:"
  echo "$REMAINING"
  echo "To remove them completely, you can use: docker rm [container_name]"
fi
