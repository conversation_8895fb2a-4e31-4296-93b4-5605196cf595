{"name": "btc-api", "version": "1.0.0", "description": "BTC API endpoint", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"axios": "0.21.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.19.2", "joi": "^17.9.2", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "mongoose": "^8.13.2", "node-cache": "^5.1.2", "ws": "^8.18.3"}, "devDependencies": {"nodemon": "^2.0.22"}}