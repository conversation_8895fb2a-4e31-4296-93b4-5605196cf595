/**
 * Application entry point
 */

// Load environment variables
require('dotenv').config();

// Load configuration
const config = require('./config');
const app = require('./src/app');
const databaseService = require('./src/services/database.service');

// Start the server
const server = app.listen(config.server.port, () => {
  console.log(`[${new Date().toISOString()}] Server is running on port ${config.server.port} in ${config.server.nodeEnv} mode`);
});

// Handle MongoDB connection errors
databaseService.connection.on('error', (err) => {
  console.error('[FATAL] MongoDB connection error:', err);
  // Close server & exit process
  server.close(() => process.exit(1));
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  console.error('[FATAL] Unhandled Promise rejection:', err);
  // Close server & exit process
  server.close(() => process.exit(1));
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.error('[FATAL] Uncaught exception:', err);
  // Close server & exit process
  server.close(() => process.exit(1));
});
