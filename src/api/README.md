# Analysis API Documentation

This API provides access to analysis results stored in the MongoDB `analysis_results` collection in the `gor_metrics` database.

## Endpoints

### GET /analysis/results

Get analysis results from the database.

**Query Parameters:**
- `primary_pattern` (required) - Primary pattern to filter by
- `symbol` (required) - Symbol to filter by (e.g., BTC/USD)
- `limit` (optional, default: 10) - Maximum number of results to return
- `week` (optional) - Filter by week number (1-53)
- `month` (optional) - Filter by month (1-12)
- `quarter` (optional) - Filter by quarter (1-4)
- `day_of_week` (optional) - Filter by day of week (0=Sunday, 6=Saturday)
- `year` (optional) - Filter by year

**Example:**
```
GET /analysis/results?primary_pattern=double_top&symbol=BTC/USD&limit=5
```

**Example with time-based filtering:**
```
GET /analysis/results?primary_pattern=double_top&symbol=BTC/USD&month=5&year=2024&day_of_week=1
```

**Response:**
```json
{
  "success": true,
  "count": 2,
  "results": [
    {
      "_id": "...",
      "primary_pattern": "double_top",
      "best_match": "bearish_reversal",
      "historical_candles": [...],
      "scores": [...],
      "created_at": "2023-06-15T10:30:00.000Z",
      "updated_at": "2023-06-15T10:30:00.000Z"
    },
    ...
  ],
  "processingTimeMs": 123
}
```

### GET /analysis/process

Process analysis data for a specific pattern and symbol.

**Query Parameters:**
- `primary_pattern` (required) - Primary pattern to process
- `symbol` (required) - Symbol to process (e.g., BTC/USD)
- `timeframe` (required) - Timeframe to process (e.g., 15min, 1h, 1d)
- `limit` (optional, default: 1) - Maximum number of results to process
- `week` (optional) - Filter by week number (1-53)
- `month` (optional) - Filter by month (1-12)
- `quarter` (optional) - Filter by quarter (1-4)
- `day_of_week` (optional) - Filter by day of week (0=Sunday, 6=Saturday)
- `year` (optional) - Filter by year

**Example:**
```
GET /analysis/process?primary_pattern=double_top&symbol=BTC/USD&timeframe=1h
```

**Example with time-based filtering:**
```
GET /analysis/process?primary_pattern=double_top&symbol=BTC/USD&timeframe=1h&quarter=2&year=2024
```

**Response:**
```json
{
  "success": true,
  "data": {
    "pattern": "double_top",
    "best_match": "bearish_reversal",
    "symbol": "BTC/USD",
    "timeframe": "1h",
    "score": 0.85,
    "candles": [...],
    "created_at": "2023-06-15T10:30:00.000Z",
    "updated_at": "2023-06-15T10:30:00.000Z"
  },
  "processingTimeMs": 456
}
```

### GET /analysis/api-call

Make external API calls based on analysis data.

**Query Parameters:**
- `primary_pattern` (required) - Primary pattern to process
- `symbol` (required) - Symbol to process (e.g., BTC/USD)
- `timeframe` (required) - Timeframe to process (e.g., 15min, 1h, 1d)
- `endpoint` (required) - External API endpoint to call
- `limit` (optional, default: 1) - Maximum number of results to process
- `week` (optional) - Filter by week number (1-53)
- `month` (optional) - Filter by month (1-12)
- `quarter` (optional) - Filter by quarter (1-4)
- `day_of_week` (optional) - Filter by day of week (0=Sunday, 6=Saturday)
- `year` (optional) - Filter by year

**Example:**
```
GET /analysis/api-call?primary_pattern=double_top&symbol=BTC/USD&timeframe=1h&endpoint=https://api.example.com/process
```

**Example with time-based filtering:**
```
GET /analysis/api-call?primary_pattern=double_top&symbol=BTC/USD&timeframe=1h&endpoint=https://api.example.com/process&day_of_week=1&month=6
```

**Response:**
```json
{
  "success": true,
  "data": {
    "analysis": {
      "pattern": "double_top",
      "best_match": "bearish_reversal",
      "symbol": "BTC/USD",
      "timeframe": "1h",
      "score": 0.85,
      "candles": [...],
      "created_at": "2023-06-15T10:30:00.000Z",
      "updated_at": "2023-06-15T10:30:00.000Z"
    },
    "api_response": {
      "status": "success",
      "prediction": "bearish",
      "confidence": 0.92
    }
  },
  "processingTimeMs": 789
}
```

### GET /analysis/test

Test endpoint to verify database connection.

**Example:**
```
GET /analysis/test
```

**Response:**
```json
{
  "success": true,
  "model": {
    "collection": "analysis_results",
    "database": "gor_metrics"
  },
  "message": "Database connection test successful"
}
```

### GET /analysis/collections

List all collections in the gor_metrics database.

**Example:**
```
GET /analysis/collections
```

**Response:**
```json
{
  "success": true,
  "database": "gor_metrics",
  "collections": ["analysis_results", "other_collection"],
  "count": 2
}
```

### GET /analysis/sample

Get a sample of data from the analysis_results collection.

**Example:**
```
GET /analysis/sample
```

**Response:**
```json
{
  "success": true,
  "collection": "analysis_results",
  "database": "gor_metrics",
  "count": 42,
  "sample": [
    {
      "_id": "...",
      "primary_pattern": "double_top",
      "best_match": "bearish_reversal",
      "historical_candles_count": 3,
      "scores_count": 5,
      "created_at": "2023-06-15T10:30:00.000Z"
    }
  ]
}
```
