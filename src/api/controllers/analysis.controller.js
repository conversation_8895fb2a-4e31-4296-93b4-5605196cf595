/**
 * Analysis Controller
 * Handles API requests for analysis data
 */
const analysisService = require('../../services/analysis.service');

/**
 * Get analysis results
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getAnalysisResults = async (req, res, next) => {
  try {
    const startTime = Date.now();
    console.log(`[${new Date().toISOString()}] Processing analysis results request:`, req.query);

    // Extract parameters
    const { primary_pattern, symbol, limit } = req.query;

    // Process request
    const results = await analysisService.getAnalysisResults({
      primary_pattern,
      symbol,
      limit
    });

    // Log processing time
    const processingTime = Date.now() - startTime;
    console.log(`[${new Date().toISOString()}] Request processed in ${processingTime}ms`);

    // Send response
    return res.json({
      success: true,
      count: results.length,
      results,
      processingTimeMs: processingTime
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Process analysis data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const processAnalysisData = async (req, res, next) => {
  try {
    const startTime = Date.now();
    console.log(`[${new Date().toISOString()}] Processing analysis data request:`, req.query);

    // Extract parameters
    const { primary_pattern, symbol, day_of_week, limit } = req.query;

    // Process request
    const result = await analysisService.processAnalysisData({
      primary_pattern,
      symbol,
      day_of_week,
      limit
    });

    // Log processing time
    const processingTime = Date.now() - startTime;
    console.log(`[${new Date().toISOString()}] Request processed in ${processingTime}ms`);

    // Send response
    return res.json({
      success: true,
      data: result,
      processingTimeMs: processingTime
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Make external API calls based on analysis data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const makeExternalApiCalls = async (req, res, next) => {
  try {
    const startTime = Date.now();
    console.log(`[${new Date().toISOString()}] Processing external API call request:`, req.query);

    // Extract parameters
    const { primary_pattern, symbol, timeframe, endpoint, limit } = req.query;

    // Process request
    const result = await analysisService.makeExternalApiCalls({
      primary_pattern,
      symbol,
      timeframe,
      endpoint,
      limit
    });

    // Log processing time
    const processingTime = Date.now() - startTime;
    console.log(`[${new Date().toISOString()}] Request processed in ${processingTime}ms`);

    // Send response
    return res.json({
      success: true,
      data: result,
      processingTimeMs: processingTime
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getAnalysisResults,
  processAnalysisData,
  makeExternalApiCalls
};
