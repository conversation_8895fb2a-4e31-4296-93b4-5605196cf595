/**
 * Bitcoin API Controller
 * Handles Bitcoin data requests
 */
const bitcoinService = require('../../services/bitcoin.service');

/**
 * Get Bitcoin data and find patterns
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getBtcData = async (req, res, next) => {
  try {
    const startTime = Date.now();
    console.log(`[${new Date().toISOString()}] Processing Bitcoin data request:`, req.query);

    // Extract parameters
    const { roundCoefficent, listRange, indexDate, resolution, m1 } = req.query;
    const compare = req.query.compare || 'true';
    const m2 = req.query.m2 || null;

    // Process request
    const result = await bitcoinService.getTopOutputApiV2(
      roundCoefficent,
      listRange,
      indexDate,
      compare,
      resolution,
      m1,
      m2
    );

    // Log processing time
    const processingTime = Date.now() - startTime;
    console.log(`[${new Date().toISOString()}] Request processed in ${processingTime}ms`);

    // Send response
    return res.json(result);
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getBtcData
};
