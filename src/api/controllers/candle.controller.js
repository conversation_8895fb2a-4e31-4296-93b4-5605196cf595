/**
 * Candle Controller
 * Handles API requests for candle data
 */
const candleService = require('../../services/candle.service');

/**
 * Fetch and store candle data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const fetchAndStore = async (req, res, next) => {
  try {
    const startTime = Date.now();
    console.log(`[${new Date().toISOString()}] Processing fetch-and-store request:`, req.body);

    // Extract parameters
    const { symbol, interval, start_date, end_date } = req.body;

    // Process request
    const result = await candleService.fetchAndStoreCandles({
      symbol,
      interval,
      start_date,
      end_date
    });

    // Log processing time
    const processingTime = Date.now() - startTime;
    console.log(`[${new Date().toISOString()}] Request processed in ${processingTime}ms`);

    // Send response
    return res.json({
      success: true,
      message: `Successfully fetched and stored candle data for ${symbol} (${interval})`,
      ...result,
      processingTimeMs: processingTime
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get time series data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getTimeSeries = async (req, res, next) => {
  try {
    const startTime = Date.now();
    console.log(`[${new Date().toISOString()}] Processing time_series request:`, req.query);

    // Extract parameters
    const { symbol, interval, start_date, end_date, outputsize } = req.query;

    // Process request
    const result = await candleService.getTimeSeries({
      symbol,
      interval,
      start_date,
      end_date,
      outputsize
    });

    // Log processing time
    const processingTime = Date.now() - startTime;
    console.log(`[${new Date().toISOString()}] Request processed in ${processingTime}ms`);

    // Send response
    return res.json(result);
  } catch (error) {
    next(error);
  }
};

module.exports = {
  fetchAndStore,
  getTimeSeries
};
