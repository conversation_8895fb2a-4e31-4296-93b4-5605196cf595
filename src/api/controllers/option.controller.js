
const deltaExchangeService = require('../../services/delta.exchange.service');
const optionUtils = require('../../utils/option.utils');
const deltaSocket = require('../../socket/delta.exchange.socket');
const OptionTicker = require('../../models/option.model');

const getGammaPerformer = async (req, res, next) => {
    try {
      const startTime = Date.now();
      console.log(`[${new Date().toISOString()}] Processing gamma performer request:`, req.query);
  
      // Extract parameters
      const { symbol, expiryDate } = req.query;
  
      // Process request
      const data = await deltaExchangeService.getDeltaOptionChain(symbol, expiryDate);


      const result = optionUtils.findBestWorstGammaOTM(data); 

      deltaSocket.launchBTCOptionsEdgeMonitor(result);
  
      // Send response
      return res.json(result);
    } catch (error) {
      next(error);
    }
  };

  const getOptionResult = async (req, res, next) => {
    try {
      const startTime = Date.now();
      console.log(`[${new Date().toISOString()}] Processing option result request:`, req.query);
  
      // Extract parameters
      const { symbol } = req.query;
  
      // Process request
      const result = await OptionTicker.aggregate([
        {
          $addFields: {
            tsDate: {
              $dateTrunc: {
                date: { $toDate: { $divide: [ "$timestamp", 1000 ] } },
                unit: "minute",
                binSize: 5
              }
            }
          }
        },
        {
          $group: {
            // _id: "$tsDate",
            _id: { 
                tsDate: "$tsDate",
                symbol: "$symbol"
              },
            avg_mark_price: { $avg: { $toDouble: "$mark_price" } },
            avg_volume: { $avg: "$volume" },
            total_turnover: { $sum: "$turnover" },
            oi_value_usd: { $last: "$oi_value_usd" },
            mark_price: { $last: "$mark_price" },
            volume: { $last: "$volume" },
            spot_price: { $last: "$spot_price" },
            symbol: { $last: "$symbol" },
            greeks: { $last: "$greeks" },
            quotes: { $last: "$quotes" },
            high: { $max: "$high" },
            low: { $min: "$low" },
            timestamp: { $last: "$timestamp" }
          }
        },
        { $sort: { timestamp: 1 } },
        { $limit: 1000 }
      ]);
      
  
      // Send response
      return res.json(result);
    } catch (error) {
      next(error);
    }
  };

module.exports = {
    getGammaPerformer,
    getOptionResult
  };
  