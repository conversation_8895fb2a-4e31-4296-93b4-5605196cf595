/**
 * Request validation middleware
 * Validates request parameters against a schema
 */

/**
 * Validate request against schema
 * @param {Object} schema - Validation schema
 * @param {string} source - Source of data to validate ('query' or 'body')
 * @returns {Function} Express middleware
 */
const validateRequest = (schema, source = 'query') => {
  return (req, res, next) => {
    const dataToValidate = source === 'body' ? req.body : req.query;
    const { error } = schema.validate(dataToValidate);

    if (error) {
      return res.status(400).json({
        error: 'Validation error',
        details: error.details.map(detail => ({
          message: detail.message,
          path: detail.path
        }))
      });
    }

    next();
  };
};

module.exports = validateRequest;
