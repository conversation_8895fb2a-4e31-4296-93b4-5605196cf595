/**
 * Analysis API Routes
 */
const express = require('express');
const analysisController = require('../controllers/analysis.controller');
const validateRequest = require('../middlewares/validate-request');
const {
  analysisResultsSchema,
  processAnalysisSchema,
  externalApiCallSchema
} = require('../validators/analysis.validator');

const router = express.Router();



/**
 * @route GET /analysis/process
 * @desc Process analysis data for a specific pattern and symbol
 */
router.get('/results', validateRequest(processAnalysisSchema, 'query'), analysisController.processAnalysisData);


module.exports = router;
