/**
 * Bitcoin API Routes
 */
const express = require('express');
const bitcoinController = require('../controllers/bitcoin.controller');
const validateRequest = require('../middlewares/validate-request');
const { bitcoinDataSchema } = require('../validators/bitcoin.validator');

const router = express.Router();

/**
 * @route GET /data-live/btc
 * @desc Get BTC data and find patterns
 */
router.get('/btc', validateRequest(bitcoinDataSchema), bitcoinController.getBtcData);

/**
 * @route GET /data-live/btc/v2
 * @desc Legacy endpoint for backward compatibility
 */
router.get('/btc/v2', validateRequest(bitcoinDataSchema), bitcoinController.getBtcData);

module.exports = router;
