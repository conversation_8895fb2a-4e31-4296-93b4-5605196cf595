/**
 * Candle API Routes
 */
const express = require('express');
const candleController = require('../controllers/candle.controller');
const validateRequest = require('../middlewares/validate-request');
const { fetchAndStoreSchema, timeSeriesSchema } = require('../validators/candle.validator');

const router = express.Router();

/**
 * @route POST /fetch-and-store
 * @desc Fetch data from TwelveData API and store in MongoDB
 */
router.post('/fetch-and-store', validateRequest(fetchAndStoreSchema, 'body'), candleController.fetchAndStore);

/**
 * @route GET /time_series
 * @desc Get time series data from MongoDB
 */
router.get('/time_series', validateRequest(timeSeriesSchema, 'query'), candleController.getTimeSeries);

module.exports = router;
