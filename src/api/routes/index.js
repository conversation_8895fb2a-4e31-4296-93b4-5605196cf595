/**
 * API Routes
 */
const express = require('express');
const bitcoinRoutes = require('./bitcoin.routes');
const candleRoutes = require('./candle.routes');
const analysisRoutes = require('./analysis.routes');
const optionRoutes = require('./option.routes');

const router = express.Router();

// Mount route groups
router.use('/data-live', bitcoinRoutes);
router.use('/analysis', analysisRoutes);
router.use('/', candleRoutes);
router.use('/option', optionRoutes);

// Health check endpoint
router.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

module.exports = router;
