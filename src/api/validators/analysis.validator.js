/**
 * Analysis API request validators
 */
const Joi = require('joi');

// Schema for analysis results request
const analysisResultsSchema = Joi.object({
  primary_pattern: Joi.string().required().description('Primary pattern to filter by'),
  symbol: Joi.string().required().description('Symbol to filter by (e.g., BTC/USD)'),
  limit: Joi.number().integer().min(1).max(100).default(10).description('Maximum number of results to return'),
  // Time-based filtering parameters (all optional)
  week: Joi.number().integer().min(1).max(53).optional().description('Filter by week number (1-53)'),
  month: Joi.number().integer().min(1).max(12).optional().description('Filter by month (1-12)'),
  quarter: Joi.number().integer().min(1).max(4).optional().description('Filter by quarter (1-4)'),
  day_of_week: Joi.number().integer().min(0).max(6).optional().description('Filter by day of week (0=Sunday, 6=Saturday)'),
  year: Joi.number().integer().min(2000).max(2100).optional().description('Filter by year')
});

// Schema for processing analysis data request
const processAnalysisSchema = Joi.object({
  primary_pattern: Joi.string().required().description('Primary pattern to process'),
  symbol: Joi.string().required().description('Symbol to process (e.g., BTC/USD)'),
  limit: Joi.number().integer().min(1).max(1000).default(1).description('Maximum number of results to process'),
  // Time-based filtering parameters (all optional)
  week: Joi.number().integer().min(1).max(53).optional().description('Filter by week number (1-53)'),
  month: Joi.number().integer().min(1).max(12).optional().description('Filter by month (1-12)'),
  quarter: Joi.number().integer().min(1).max(4).optional().description('Filter by quarter (1-4)'),
  day_of_week: Joi.number().integer().min(0).max(6).optional().description('Filter by day of week (0=Sunday, 6=Saturday)'),
  year: Joi.number().integer().min(2000).max(2100).optional().description('Filter by year')
});

// Schema for making external API calls
const externalApiCallSchema = Joi.object({
  primary_pattern: Joi.string().required().description('Primary pattern to process'),
  symbol: Joi.string().required().description('Symbol to process (e.g., BTC/USD)'),
  timeframe: Joi.string().required().description('Timeframe to process (e.g., 15min, 1h, 1d)'),
  endpoint: Joi.string().uri().required().description('External API endpoint to call'),
  limit: Joi.number().integer().min(1).max(10).default(1).description('Maximum number of results to process'),
  // Time-based filtering parameters (all optional)
  week: Joi.number().integer().min(1).max(53).optional().description('Filter by week number (1-53)'),
  month: Joi.number().integer().min(1).max(12).optional().description('Filter by month (1-12)'),
  quarter: Joi.number().integer().min(1).max(4).optional().description('Filter by quarter (1-4)'),
  day_of_week: Joi.number().integer().min(0).max(6).optional().description('Filter by day of week (0=Sunday, 6=Saturday)'),
  year: Joi.number().integer().min(2000).max(2100).optional().description('Filter by year')
});

module.exports = {
  analysisResultsSchema,
  processAnalysisSchema,
  externalApiCallSchema
};
