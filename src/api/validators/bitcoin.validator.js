/**
 * Bitcoin API request validators
 */
const Joi = require('joi');

// Schema for Bitcoin data request
const bitcoinDataSchema = Joi.object({
  roundCoefficent: Joi.number().required().description('Coefficient for rounding'),
  listRange: Joi.number().integer().min(1).required().description('Range of data points to process'),
  indexDate: Joi.number().required().description('Reference date as Unix timestamp'),
  compare: Joi.string().valid('true', 'false').default('true').description('Comparison mode'),
  resolution: Joi.string().required().description('Time resolution (e.g., 1d, 1h)'),
  m1: Joi.string().required().description('Primary symbol code'),
  m2: Joi.string().optional().description('Secondary symbol code')
});

module.exports = {
  bitcoinDataSchema,
  btcDataSchema: bitcoinDataSchema // For backward compatibility
};
