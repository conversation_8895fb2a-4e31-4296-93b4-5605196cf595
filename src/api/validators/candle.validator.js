/**
 * Candle API request validators
 */
const Joi = require('joi');

// Schema for fetch-and-store request
const fetchAndStoreSchema = Joi.object({
  symbol: Joi.string().required().description('Trading symbol (e.g., BTC/USD)'),
  interval: Joi.string().required().description('Time interval (e.g., 15min, 1h, 1d)'),
  start_date: Joi.string().optional().description('Start date (YYYY-MM-DD or YYYY-MM-DD HH:MM:SS)'),
  end_date: Joi.string().optional().description('End date (YYYY-MM-DD or YYYY-MM-DD HH:MM:SS)')
});

// Schema for time_series request
const timeSeriesSchema = Joi.object({
  symbol: Joi.string().required().description('Trading symbol (e.g., BTC/USD)'),
  interval: Joi.string().required().description('Time interval (e.g., 15min, 1h, 1d)'),
  start_date: Joi.string().optional().description('Start date (YYYY-MM-DD or YYYY-MM-DD HH:MM:SS)'),
  end_date: Joi.string().optional().description('End date (YYYY-MM-DD or YYYY-MM-DD HH:MM:SS)'),
  outputsize: Joi.number().integer().min(1).max(5000).default(30).description('Number of candles to return')
});

module.exports = {
  fetchAndStoreSchema,
  timeSeriesSchema
};
