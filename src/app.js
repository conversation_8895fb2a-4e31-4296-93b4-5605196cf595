/**
 * Express application setup
 */
const express = require('express');
const cors = require('cors');
const config = require('../config');
const routes = require('./api/routes');
const databaseService = require('./services/database.service');

// Create Express application
const app = express();

// Connect to MongoDB
databaseService.connect()
  .then(() => {
    console.log(`[${new Date().toISOString()}] MongoDB connection established`);
  })
  .catch(err => {
    console.error(`[${new Date().toISOString()}] MongoDB connection error:`, err.message);
    process.exit(1);
  });

// Apply middlewares
app.use(cors());
app.use(express.json());

// CORS headers
app.use(function (req, res, next) {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  res.setHeader('Access-Control-Allow-Credentials', true);
  next();
});

// Log all requests
app.use((req, res, next) => {
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
  next();
});

// Mount API routes
app.use('/', routes);

// Handle 404 errors
app.use((req, res) => {
  console.log(`[${new Date().toISOString()}] 404 Not Found: ${req.method} ${req.url}`);
  res.status(404).json({ error: 'Not Found' });
});

// Error handling middleware
app.use((err, req, res, next) => {
  const statusCode = err.statusCode || 500;
  console.error(`[${new Date().toISOString()}] Error: ${err.message}`);

  res.status(statusCode).json({
    error: err.message,
    stack: config.server.isDev ? err.stack : undefined
  });
});

// Handle process termination
process.on('SIGINT', async () => {
  console.log(`[${new Date().toISOString()}] Received SIGINT signal, shutting down gracefully`);
  await databaseService.disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log(`[${new Date().toISOString()}] Received SIGTERM signal, shutting down gracefully`);
  await databaseService.disconnect();
  process.exit(0);
});

module.exports = app;
