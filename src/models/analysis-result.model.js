/**
 * Analysis Result Model
 * Represents data from the analysis_results collection
 */
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const { analysisConnection } = require('../services/database.service');

// Define nested schemas for complex structures
const TimeframeSchema = new Schema({
  timeframe: { type: String, required: true },
  candles: [Schema.Types.Mixed]
}, { _id: false });

const HistoricalCandlesSchema = new Schema({
  symbol: { type: String, required: true },
  timeframes: [TimeframeSchema]
}, { _id: false });

const ScoreSchema = new Schema({
  symbol: { type: String, required: true },
  score: { type: Number, required: true }
}, { _id: false });

/**
 * AnalysisResult Schema
 * Stores pattern analysis results
 */
const analysisResultSchema = new Schema({
  // Primary pattern identifier
  primary_pattern: {
    type: String,
    required: true,
    index: true
  },

  // Best matching pattern
  best_match: {
    type: String,
    required: true,
    index: true
  },

  // Historical candle data organized by symbol and timeframe
  historical_candles: [HistoricalCandlesSchema],

  // Scores for different symbols
  scores: [ScoreSchema],

  // Metadata
  created_at: {
    type: Date,
    default: Date.now,
    index: true
  },

  updated_at: {
    type: Date,
    default: Date.now
  }
});

// Create model from schema, specifying the collection name explicitly
// Use the analysisConnection instead of the default mongoose connection
const AnalysisResult = analysisConnection.model('AnalysisResult', analysisResultSchema, 'analysis_results');

module.exports = AnalysisResult;
