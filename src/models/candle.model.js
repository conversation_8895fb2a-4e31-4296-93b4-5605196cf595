/**
 * Candle Data Model
 * Represents a single candle in a time series
 */
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * CandleData Schema
 * Stores individual candle data points
 */
const candleDataSchema = new Schema({
  // Symbol identifier (e.g., "BTC/USD")
  symbol: {
    type: String,
    required: true,
    index: true
  },
  
  // Time interval (e.g., "15min", "1h", "1d")
  interval: {
    type: String,
    required: true,
    index: true
  },
  
  // Datetime of the candle (stored in Asia/Kolkata timezone)
  datetime: {
    type: Date,
    required: true,
    index: true
  },
  
  // OHLC values (stored as strings to preserve precision)
  open: {
    type: String,
    required: true
  },
  
  high: {
    type: String,
    required: true
  },
  
  low: {
    type: String,
    required: true
  },
  
  close: {
    type: String,
    required: true
  },
  
  // Optional volume data
  volume: {
    type: String,
    default: null
  },
  
  // Metadata
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Create a compound index for faster querying
candleDataSchema.index({ symbol: 1, interval: 1, datetime: 1 }, { unique: true });

// Create model from schema
const CandleData = mongoose.model('CandleData', candleDataSchema);

module.exports = CandleData;
