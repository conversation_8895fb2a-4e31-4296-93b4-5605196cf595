/**
 * Meta Information Model
 * Stores metadata about symbols
 */
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * MetaInfo Schema
 * Stores metadata about symbols from TwelveData API
 */
const metaInfoSchema = new Schema({
  // Symbol identifier (e.g., "BTC/USD")
  symbol: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  
  // Base currency (e.g., "Bitcoin")
  currency_base: {
    type: String,
    required: true
  },
  
  // Quote currency (e.g., "US Dollar")
  currency_quote: {
    type: String,
    required: true
  },
  
  // Exchange name (e.g., "Coinbase Pro")
  exchange: {
    type: String,
    required: true
  },
  
  // Asset type (e.g., "Digital Currency")
  type: {
    type: String,
    required: true
  },
  
  // Last updated timestamp
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Create model from schema
const MetaInfo = mongoose.model('MetaInfo', metaInfoSchema);

module.exports = MetaInfo;
