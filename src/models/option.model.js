const mongoose = require('mongoose');

const GreeksSchema = new mongoose.Schema({
    delta: String,
    gamma: String,
    rho: String,
    spot: String,
    theta: String,
    vega: String
}, { _id: false });

const PriceBandSchema = new mongoose.Schema({
    lower_limit: String,
    upper_limit: String
}, { _id: false });

const QuotesSchema = new mongoose.Schema({
    ask_iv: String,
    ask_size: String,
    best_ask: String,
    best_ask_mm: { type: String, default: null },
    best_bid: String,
    best_bid_mm: { type: String, default: null },
    bid_iv: String,
    bid_size: String,
    impact_mid_price: { type: String, default: null },
    mark_iv: String
}, { _id: false });

const OptionTickerSchema = new mongoose.Schema({
    oi_change_usd_6h: String,
    greeks: GreeksSchema,
    turnover: Number,
    size: Number,
    timestamp: Number,
    open: Number,
    contract_type: String,
    turnover_usd: Number,
    product_id: Number,
    price_band: PriceBandSchema,
    oi_value_symbol: String,
    close: Number,
    oi_value_usd: String,
    type: String,
    mark_price: String,
    volume: Number,
    oi: String,
    quotes: QuotesSchema,
    high: Number,
    oi_value: String,
    symbol: String,
    spot_price: String,
    low: Number,
    initial_margin: String,
    tick_size: String,
    underlying_asset_symbol: String,
    strike_price: String,
    oi_contracts: String,
    tags: { type: [String], default: [] },
    turnover_symbol: String,
    mark_change_24h: String,
    mark_vol: String,
    description: String,
    time: String
});

module.exports = mongoose.model('OptionTicker', OptionTickerSchema);
