/**
 * Analysis Service
 * Handles operations related to analysis results data
 */
const axios = require('axios');
const AnalysisResult = require('../models/analysis-result.model');

/**
 * Get analysis results from database
 * @param {Object} params - Query parameters
 * @returns {Promise<Array>} - Analysis results
 */
const getAnalysisResults = async (params) => {
  try {
    const {
      primary_pattern,
      symbol,
      limit = 100,
      week,
      month,
      quarter,
      day_of_week,
      year
    } = params;

    console.log(`[${new Date().toISOString()}] Getting analysis results with params:`, params);

    // Build query
    const query = {};

    if (primary_pattern) {
      query['primary_pattern.symbol'] = primary_pattern;
    }

    if (symbol) {
      // Find results where the symbol exists in historical_candles
      query['historical_candles.symbol'] = symbol;
    }

    // Add time-based filters if provided
    // We'll use the primary_pattern.end_date field for time-based filtering
    const dateFilters = {};

    if (year) {
      dateFilters.$expr = {
        $eq: [
          {
            $year: {
              $dateFromString: {
                dateString: "$primary_pattern.end_date",
                format: "%Y-%m-%d %H:%M:%S"
              }
            }
          },
          parseInt(year)
        ]
      };
    }

    if (month) {
      const monthFilter = {
        $eq: [
          {
            $month: {
              $dateFromString: {
                dateString: "$primary_pattern.end_date",
                format: "%Y-%m-%d %H:%M:%S"
              }
            }
          },
          parseInt(month)
        ]
      };

      if (!dateFilters.$expr) {
        dateFilters.$expr = monthFilter;
      } else {
        // If we already have a year filter, we need to use $and
        dateFilters.$expr = {
          $and: [
            dateFilters.$expr,
            monthFilter
          ]
        };
      }
    }

    if (week) {
      const weekFilter = {
        $eq: [
          {
            $week: {
              $dateFromString: {
                dateString: "$primary_pattern.end_date",
                format: "%Y-%m-%d %H:%M:%S"
              }
            }
          },
          parseInt(week)
        ]
      };

      if (!dateFilters.$expr) {
        dateFilters.$expr = weekFilter;
      } else {
        // If we already have other filters, we need to use $and
        if (dateFilters.$expr.$and) {
          dateFilters.$expr.$and.push(weekFilter);
        } else {
          dateFilters.$expr = {
            $and: [
              dateFilters.$expr,
              weekFilter
            ]
          };
        }
      }
    }

    if (quarter) {
      const quarterFilter = {
        $eq: [
          {
            $ceil: {
              $divide: [
                {
                  $month: {
                    $dateFromString: {
                      dateString: "$primary_pattern.end_date",
                      format: "%Y-%m-%d %H:%M:%S"
                    }
                  }
                },
                3
              ]
            }
          },
          parseInt(quarter)
        ]
      };

      if (!dateFilters.$expr) {
        dateFilters.$expr = quarterFilter;
      } else {
        // If we already have other filters, we need to use $and
        if (dateFilters.$expr.$and) {
          dateFilters.$expr.$and.push(quarterFilter);
        } else {
          dateFilters.$expr = {
            $and: [
              dateFilters.$expr,
              quarterFilter
            ]
          };
        }
      }
    }

    if (day_of_week !== undefined) {
      // For string dates, we need to use $dateFromString to convert to a Date object first
      const dowFilter = {
        $eq: [
          {
            $dayOfWeek: {
              $dateFromString: {
                dateString: "$primary_pattern.end_date",
                format: "%Y-%m-%d %H:%M:%S" // Format matches "2024-01-01 21:30:00"
              }
            }
          },
          parseInt(day_of_week) + 1
        ]
      }; // MongoDB dayOfWeek is 1-7 (Sunday=1)

      if (!dateFilters.$expr) {
        dateFilters.$expr = dowFilter;
      } else {
        // If we already have other filters, we need to use $and
        if (dateFilters.$expr.$and) {
          dateFilters.$expr.$and.push(dowFilter);
        } else {
          dateFilters.$expr = {
            $and: [
              dateFilters.$expr,
              dowFilter
            ]
          };
        }
      }
    }

    // Add date filters to the main query if any were specified
    if (dateFilters.$expr) {
      Object.assign(query, dateFilters);
    }

    // Execute query
    const results = await AnalysisResult.find(query)
      .sort({ created_at: -1 })
      .limit(parseInt(limit))
      .lean();

    console.log(`[${new Date().toISOString()}] Found ${results.length} analysis results`);

    return results;
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error getting analysis results:`, error.message);
    throw error;
  }
};

/**
 * Make API calls based on analysis results
 * @param {Object} params - Parameters for the API request
 * @returns {Promise<Object>} - Result of the API calls
 */
const processAnalysisData = async (params) => {
  try {
    const {
      primary_pattern,
      symbol,
      limit,
      week,
      month,
      quarter,
      day_of_week,
      year
    } = params;

    console.log(`[${new Date().toISOString()}] Processing analysis data for pattern: ${primary_pattern}, symbol: ${symbol}`);

    // Get analysis results
    const query = {
      'primary_pattern.symbol': primary_pattern,
      'best_match.symbol': symbol,
      'primary_pattern.start_date': { $gte : "2024-01-01 00:00:00" }
    };

    // Add time-based filters if provided
    // We'll use the primary_pattern.end_date field for time-based filtering
    const dateFilters = {};

    if (year) {
      dateFilters.$expr = {
        $eq: [
          {
            $year: {
              $dateFromString: {
                dateString: "$primary_pattern.end_date",
                format: "%Y-%m-%d %H:%M:%S"
              }
            }
          },
          parseInt(year)
        ]
      };
    }

    if (month) {
      const monthFilter = {
        $eq: [
          {
            $month: {
              $dateFromString: {
                dateString: "$primary_pattern.end_date",
                format: "%Y-%m-%d %H:%M:%S"
              }
            }
          },
          parseInt(month)
        ]
      };

      if (!dateFilters.$expr) {
        dateFilters.$expr = monthFilter;
      } else {
        // If we already have a year filter, we need to use $and
        dateFilters.$expr = {
          $and: [
            dateFilters.$expr,
            monthFilter
          ]
        };
      }
    }

    if (week) {
      const weekFilter = {
        $eq: [
          {
            $week: {
              $dateFromString: {
                dateString: "$primary_pattern.end_date",
                format: "%Y-%m-%d %H:%M:%S"
              }
            }
          },
          parseInt(week)
        ]
      };

      if (!dateFilters.$expr) {
        dateFilters.$expr = weekFilter;
      } else {
        // If we already have other filters, we need to use $and
        if (dateFilters.$expr.$and) {
          dateFilters.$expr.$and.push(weekFilter);
        } else {
          dateFilters.$expr = {
            $and: [
              dateFilters.$expr,
              weekFilter
            ]
          };
        }
      }
    }

    if (quarter) {
      const quarterFilter = {
        $eq: [
          {
            $ceil: {
              $divide: [
                {
                  $month: {
                    $dateFromString: {
                      dateString: "$primary_pattern.end_date",
                      format: "%Y-%m-%d %H:%M:%S"
                    }
                  }
                },
                3
              ]
            }
          },
          parseInt(quarter)
        ]
      };

      if (!dateFilters.$expr) {
        dateFilters.$expr = quarterFilter;
      } else {
        // If we already have other filters, we need to use $and
        if (dateFilters.$expr.$and) {
          dateFilters.$expr.$and.push(quarterFilter);
        } else {
          dateFilters.$expr = {
            $and: [
              dateFilters.$expr,
              quarterFilter
            ]
          };
        }
      }
    }

    if (day_of_week !== undefined) {
      // For string dates, we need to use $dateFromString to convert to a Date object first
      const dowFilter = {
        $eq: [
          {
            $dayOfWeek: {
              $dateFromString: {
                dateString: "$primary_pattern.end_date",
                format: "%Y-%m-%d %H:%M:%S" // Format matches "2024-01-01 21:30:00"
              }
            }
          },
          parseInt(day_of_week) + 1
        ]
      }; // MongoDB dayOfWeek is 1-7 (Sunday=1)

      if (!dateFilters.$expr) {
        dateFilters.$expr = dowFilter;
      } else {
        // If we already have other filters, we need to use $and
        if (dateFilters.$expr.$and) {
          dateFilters.$expr.$and.push(dowFilter);
        } else {
          dateFilters.$expr = {
            $and: [
              dateFilters.$expr,
              dowFilter
            ]
          };
        }
      }
    }

    // Add date filters to the main query if any were specified
    if (dateFilters.$expr) {
      Object.assign(query, dateFilters);
    }

    console.log(`[${new Date().toISOString()}] Query:`, JSON.stringify(query));

    let results = await AnalysisResult.find(query)
      .sort({ created_at: 1 })
      .limit(parseInt(limit))
      .lean();

    if (!results || results.length === 0) {
      throw new Error(`No analysis results found for pattern: ${primary_pattern} and symbol: ${symbol}`);
    }





    console.log(`[${new Date().toISOString()}] Filtered to ${results.length} valid results`);

    return results;

  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error processing analysis data:`, error.message);
    throw error;
  }
};

/**
 * Make external API calls based on analysis results
 * @param {Object} params - Parameters for the API request
 * @returns {Promise<Object>} - Result of the external API calls
 */
const makeExternalApiCalls = async (params) => {
  try {
    const {
      primary_pattern,
      symbol,
      endpoint,
      limit,
      week,
      month,
      quarter,
      day_of_week,
      year
    } = params;

    console.log(`[${new Date().toISOString()}] Making external API calls based on analysis for pattern: ${primary_pattern} and symbol: ${symbol}`);

    // Get analysis data
    const analysisData = await processAnalysisData({
      primary_pattern,
      symbol,
      limit,
      week,
      month,
      quarter,
      day_of_week,
      year
    });

    // Prepare data for external API call
    // This is a placeholder - you'll need to customize this based on your specific API requirements
    const apiRequestData = {
      pattern: analysisData.pattern,
      best_match: analysisData.best_match,
      symbol: analysisData.symbol,
      score: analysisData.score,
      candle_count: analysisData.candles.length
    };

    // Make the external API call
    // Replace this with your actual API endpoint and parameters
    // Using GET request with query parameters instead of POST
    const apiResponse = await axios.get(endpoint, {
      params: apiRequestData,
      headers: { 'Accept': 'application/json' },
      timeout: 30000 // 30 second timeout
    });

    // Return combined results
    return {
      analysis: analysisData,
      api_response: apiResponse.data
    };
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error making external API calls:`, error.message);
    throw error;
  }
};

module.exports = {
  getAnalysisResults,
  processAnalysisData,
  makeExternalApiCalls
};
