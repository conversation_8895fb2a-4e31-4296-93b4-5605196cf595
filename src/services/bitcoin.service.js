/**
 * Bitcoin Service
 * Business logic for Bitcoin data processing
 */
const axios = require('axios');
const moment = require('moment');
const NodeCache = require('node-cache');
const config = require('../../config');
const symbolUtils = require('../utils/symbol.utils');
const chartUtils = require('../utils/chart.utils');
const requestUtils = require('../utils/request.utils');

// Initialize cache with settings from configuration
const myCache = new NodeCache();

// Constants from configuration
const BTC_API_CACHE_KEY = config.cache.prefix;
const BTC_API_CACHE_TTL = config.cache.ttl;
const CACHE_ENABLED = config.cache.enabled;
const DEFAULT_TIMEZONE = config.timezone.default;

/**
 * Fetch BTC data from Twelve Data API with caching
 * @param {string} _ - Unused date parameter (kept for backward compatibility)
 * @param {string} resolution - Time resolution (e.g., '1d', '1h')
 * @param {string} symbol - Trading symbol (default: "BTC/USD")
 * @returns {Promise<Object>} - API response data
 */
const fetchBtcData = async(indexDate, resolution, symbol = "BTC/USD") => {
  // Check if caching is enabled
  if (CACHE_ENABLED) {
    // Check cache first
    const cacheKey = `${BTC_API_CACHE_KEY}_${symbol}_${resolution}`;
    const cachedData = myCache.get(cacheKey);

    if (cachedData) {
      console.log(`[${new Date().toISOString()}] Using cached data for ${symbol} at ${resolution}`);
      return cachedData;
    }
  } else {
    console.log(`[${new Date().toISOString()}] Cache is disabled by configuration`);
  }

  // Map resolution formats
  const resolutionMap = {
    '1d': '1day',
    '1min': '1min',
    '5min': '5min',
    '15min': '15min',
    '30min': '30min',
    '45min': '45min',
    '1h': '1h',
    '4h': '4h',
    '1w': '1week',
    '1month': '1month'
  };

  // Convert resolution format if needed
  const apiResolution = resolutionMap[resolution] || resolution;

  // Get API key from configuration
  const apiKey = config.api.twelveDataApiKey;

  const params = {
    interval: apiResolution,
    symbol: symbol,
    apikey: apiKey,
    outputsize: 5000,
    timezone: DEFAULT_TIMEZONE,
    end_date: indexDate
  };

  try {
    console.log(`[${new Date().toISOString()}] Fetching data for ${symbol} at ${resolution}`);
    const response = await axios.get('https://api.twelvedata.com/time_series', {
      params,
      headers: { 'Accept': 'application/json' },
      timeout: 20000 // 20 second timeout
    });

    if (!response.data || !response.data.values || !response.data.values.length) {
      throw new Error('Invalid response from Twelve Data API');
    }

    // Log data range
    const startDate = response.data.values[response.data.values.length - 1]["datetime"];
    const endDate = response.data.values[0]["datetime"];
    console.log(`[${new Date().toISOString()}] Data received from ${startDate} to ${endDate}`);

    // Cache the result if caching is enabled
    if (CACHE_ENABLED) {
      const cacheKey = `${BTC_API_CACHE_KEY}_${symbol}_${resolution}`;
      myCache.set(cacheKey, response.data, BTC_API_CACHE_TTL);
    }
    return response.data;
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error fetching data:`, error.message);
    throw error; // Re-throw to handle in the calling function
  }
};

/**
 * Get BTC data and process it for chart visualization
 * @param {number|string} roundCoefficent - Coefficient for rounding
 * @param {number|string} listRange - Range of data points to process
 * @param {number|string} indexDate - Reference date as Unix timestamp
 * @param {string} compare - Comparison mode ('true' or 'false')
 * @param {string} resolution - Time resolution (e.g., '1d', '1h')
 * @param {string} v1 - Primary symbol code (default: 'BTC')
 * @param {string} v2 - Secondary symbol code (optional)
 * @returns {Promise<Object>} - Processed data for visualization
 */
const getTopOutputApiV2 = async (roundCoefficent, listRange, indexDate, compare, resolution, v1 = 'BTC', v2) => {
  console.log(`[${new Date().toISOString()}] Processing request: ${v1}${v2 ? ' vs ' + v2 : ''} at ${resolution}`);

  // Parse and validate parameters
  try {
    listRange = parseInt(listRange);
    if (isNaN(listRange) || listRange <= 0) {
      throw new Error('Invalid listRange parameter');
    }

    indexDate = Number.parseFloat(indexDate);
    if (isNaN(indexDate)) {
      throw new Error('Invalid indexDate parameter');
    }
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Parameter validation error:`, error.message);
    throw error;
  }

  let array = [];

  // Format date based on resolution
  const formattedDate = resolution === ('1d' || '1w')
    ? moment.unix(indexDate).format('YYYY-MM-DD')
    : moment.unix(indexDate).local().format('YYYY-MM-DD HH:mm:ss');

  // Get symbol for primary asset
  const symbol1 = symbolUtils.getSymbol(v1);
  if (!symbol1) {
    throw new Error(`Unknown symbol: ${v1}`);
  }

  // Initialize request functions array
  const requestFunctions = [
    () => fetchBtcData(indexDate, resolution, symbol1)
  ];

  // Initialize secondary asset data
  let symbol2 = null;

  // If secondary asset is specified, add it to the request functions
  if (v2) {
    symbol2 = symbolUtils.getSymbol(v2);
    if (!symbol2) {
      throw new Error(`Unknown symbol: ${v2}`);
    }
    requestFunctions.push(() => fetchBtcData(indexDate, resolution, symbol2));
  }

  // Execute requests in parallel
  console.log(`[${new Date().toISOString()}] Executing ${requestFunctions.length} data requests in parallel`);
  const results = await requestUtils.executeParallelRequests(requestFunctions);

  // Extract results
  const obj1 = results[0];
  const obj2 = results.length > 1 ? results[1] : null;

  // Add symbol to values
  const obj1withSymbol = {
    ...obj1,
    values: obj1.values.map(value => ({
      ...value,
      symbol: obj1.meta.symbol
    }))
  };

  // Prepare data
  let data = { values: [...obj1withSymbol.values] };

  // Add second symbol data if available
  if (obj2) {
    const obj2withSymbol = {
      ...obj2,
      values: obj2.values.map(value => ({
        ...value,
        symbol: obj2.meta.symbol
      }))
    };
    data.values = [...obj1withSymbol.values, ...obj2withSymbol.values];
  }

  // Process data more efficiently
  array = data.values.map(jsonData => ({
    label: jsonData.symbol,
    close: jsonData.close,
    high: jsonData.high,
    low: jsonData.low,
    open: jsonData.open,
    volume: jsonData.volume,
    action: jsonData.close,  // primary method
    date: jsonData.datetime
  })).slice(0, 30000); // Limit to 30,000 data points for performance

  console.log(`[${new Date().toISOString()}] Processed ${array.length} data points`);

  // Find the index of the requested date
  const dateOfIndex = array.findIndex(v => v.date === formattedDate);
  if(dateOfIndex === -1) {
    console.log(`[${new Date().toISOString()}] Date not found: ${formattedDate}`);
    return {error: "Date not found", requestedDate: formattedDate};
  }

  console.log(`[${new Date().toISOString()}] Found date at index ${dateOfIndex}, value: ${array[dateOfIndex].action}`);

  // Get the reference data slice
  const referenceSlice = array.slice(dateOfIndex, dateOfIndex + listRange);

  // Find valid sequences
  const sequenceIndices = chartUtils.findValidSequencesV2(
    array,
    referenceSlice,
    roundCoefficent,
    compare
  );

  // Log results
  if (sequenceIndices.length > 0) {
    console.log(`[${new Date().toISOString()}] Found ${sequenceIndices.length} matching sequences`);
  } else {
    console.log(`[${new Date().toISOString()}] No matching sequences found`);
  }

  // Process sequences more efficiently using flatMap
  const listOfSequence = sequenceIndices.flatMap(seq =>
    array.slice(seq, seq + listRange)
  );

  // More efficient data conversion function
  function convertListToFixedSize(list, size) {
    if (!list.length) {
      return {}; // Return empty object if no sequences found
    }

    const graphHeader = ["labels", "datasets", "data", "label"];
    const numChunks = Math.ceil(list.length / size);

    // Use object literal instead of building incrementally
    return Array.from({ length: numChunks }, (_, i) => {
      const chunk = list.slice(i * size, (i + 1) * size);
      const prices = chunk.map(a => a.action).reverse();
      const dates = chunk.map(a => a.date).reverse();
      const labels = chunk.map(a => a.label);

      return {
        index: i,
        chartData: {
          [graphHeader[0]]: dates,
          [graphHeader[1]]: [{
            [graphHeader[2]]: prices,
            [graphHeader[3]]: [labels[0]]
          }]
        }
      };
    }).reduce((obj, item) => {
      obj[item.index] = item.chartData;
      return obj;
    }, {});
  }

  // Convert and process the data
  const startTime = Date.now();
  const convertedList = convertListToFixedSize(listOfSequence, listRange);
  const result = chartUtils.findAreaRelativeValueDifference(convertedList, 10);

  const processingTime = Date.now() - startTime;
  console.log(`[${new Date().toISOString()}] Data processing completed in ${processingTime}ms`);

  return result;
};

module.exports = {
  fetchBtcData,
  getTopOutputApiV2
};
