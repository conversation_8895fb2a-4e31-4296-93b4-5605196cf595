/**
 * Candle Service
 * Handles operations related to candle data
 */
const moment = require('moment-timezone');
const CandleData = require('../models/candle.model');
const MetaInfo = require('../models/meta.model');
const config = require('../../config');
const twelveDataService = require('./twelvedata.service');

// Default timezone from configuration
const DEFAULT_TIMEZONE = config.timezone.default;

/**
 * Save candle data to database
 * @param {Object} candleData - Candle data from TwelveData API
 * @returns {Promise<Object>} - Result of the operation
 */
const saveCandleData = async (candleData) => {
  try {
    if (!candleData || !candleData.meta || !candleData.values || !candleData.values.length) {
      throw new Error('Invalid candle data format');
    }

    const { symbol, interval, currency_base, currency_quote, exchange, type } = candleData.meta;
    const values = candleData.values;

    console.log(`[${new Date().toISOString()}] Saving ${values.length} candles for ${symbol} (${interval})`);

    // Save or update meta information
    await MetaInfo.findOneAndUpdate(
      { symbol },
      {
        symbol,
        currency_base,
        currency_quote,
        exchange,
        type,
        updatedAt: new Date()
      },
      { upsert: true, new: true }
    );

    // Prepare bulk operations for candle data
    const operations = values.map(candle => {
      // Convert datetime to Date object in the specified timezone
      const datetimeInTz = moment.tz(candle.datetime, DEFAULT_TIMEZONE).toDate();

      return {
        updateOne: {
          filter: { symbol, interval, datetime: datetimeInTz },
          update: {
            symbol,
            interval,
            datetime: datetimeInTz,
            open: candle.open,
            high: candle.high,
            low: candle.low,
            close: candle.close,
            volume: candle.volume || null
          },
          upsert: true
        }
      };
    });

    // Execute bulk operations if there are any
    if (operations.length > 0) {
      const result = await CandleData.bulkWrite(operations);
      console.log(`[${new Date().toISOString()}] Saved ${result.upsertedCount} new candles, modified ${result.modifiedCount} existing candles`);
      return {
        success: true,
        upsertedCount: result.upsertedCount,
        modifiedCount: result.modifiedCount,
        totalProcessed: operations.length
      };
    }

    return { success: true, totalProcessed: 0 };
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error saving candle data:`, error.message);
    throw error;
  }
};

/**
 * Fetch and store candle data from TwelveData API
 * @param {Object} params - Parameters for the API request
 * @returns {Promise<Object>} - Result of the operation
 */
const fetchAndStoreCandles = async (params) => {
  try {
    const { symbol, interval, start_date, end_date } = params;

    if (!symbol || !interval) {
      throw new Error('Symbol and interval are required');
    }

    console.log(`[${new Date().toISOString()}] Fetching candles for ${symbol} (${interval})`);

    // Initialize variables for pagination
    let currentEndDate = end_date ? moment.tz(end_date, DEFAULT_TIMEZONE) : moment.tz(DEFAULT_TIMEZONE);
    const startDateObj = start_date ? moment.tz(start_date, DEFAULT_TIMEZONE) : null;
    let allCandlesCount = 0;
    let hasMoreData = true;
    let retryCount = 0;
    const MAX_RETRIES = 3;
    const BATCH_SIZE = 5000; // Maximum number of candles per request

    // Fetch data in batches until we have all data or reach the start date
    while (hasMoreData && retryCount < MAX_RETRIES) {
      try {
        // Format end_date for API request
        const formattedEndDate = currentEndDate.format('YYYY-MM-DD HH:mm:ss');

        // Fetch data from TwelveData API
        const candleData = await twelveDataService.fetchTimeSeries({
          symbol,
          interval,
          end_date: formattedEndDate,
          outputsize: BATCH_SIZE
        });

        // Save data to database
        const saveResult = await saveCandleData(candleData);
        allCandlesCount += saveResult.upsertedCount;

        // Check if we need to fetch more data
        const oldestCandleDate = candleData.values.length > 0
          ? moment.tz(candleData.values[candleData.values.length - 1].datetime, DEFAULT_TIMEZONE)
          : null;

        // If we have a start date and the oldest candle is before or equal to it, we're done
        if (startDateObj && oldestCandleDate && oldestCandleDate.isSameOrBefore(startDateObj)) {
          hasMoreData = false;
          console.log(`[${new Date().toISOString()}] Reached start date, stopping pagination`);
        }
        // If we got fewer candles than the batch size, we've reached the end of available data
        else if (candleData.values.length < BATCH_SIZE) {
          hasMoreData = false;
          console.log(`[${new Date().toISOString()}] Reached end of available data`);
        }
        // Otherwise, update the end date for the next batch
        else if (oldestCandleDate) {
          currentEndDate = oldestCandleDate;
          // Add a small delay to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 1000));
        } else {
          hasMoreData = false;
        }

        // Reset retry count on success
        retryCount = 0;
      } catch (error) {
        console.error(`[${new Date().toISOString()}] Error fetching batch:`, error.message);
        retryCount++;

        if (retryCount >= MAX_RETRIES) {
          console.error(`[${new Date().toISOString()}] Max retries reached, stopping pagination`);
          throw new Error(`Failed to fetch data after ${MAX_RETRIES} retries: ${error.message}`);
        }

        // Exponential backoff
        const backoffTime = Math.pow(2, retryCount) * 1000;
        console.log(`[${new Date().toISOString()}] Retrying in ${backoffTime}ms (attempt ${retryCount}/${MAX_RETRIES})`);
        await new Promise(resolve => setTimeout(resolve, backoffTime));
      }
    }

    return {
      success: true,
      symbol,
      interval,
      newCandlesStored: allCandlesCount
    };
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error in fetchAndStoreCandles:`, error.message);
    throw error;
  }
};

/**
 * Get candle data from database
 * @param {Object} params - Query parameters
 * @returns {Promise<Object>} - Candle data in TwelveData API format
 */
const getTimeSeries = async (params) => {
  try {
    const { symbol, interval, start_date, end_date, outputsize = 30 } = params;

    if (!symbol || !interval) {
      throw new Error('Symbol and interval are required');
    }

    console.log(`[${new Date().toISOString()}] Getting time series for ${symbol} (${interval})`);

    // Build query
    const query = { symbol, interval };

    // Add date range if provided
    if (start_date || end_date) {
      query.datetime = {};

      if (start_date) {
        query.datetime.$gte = moment.tz(start_date, DEFAULT_TIMEZONE).toDate();
      }

      if (end_date) {
        query.datetime.$lte = moment.tz(end_date, DEFAULT_TIMEZONE).toDate();
      }
    }

    // Get meta information
    const metaInfo = await MetaInfo.findOne({ symbol });
    if (!metaInfo) {
      throw new Error(`No meta information found for symbol: ${symbol}`);
    }

    // Determine query strategy based on parameters
    let candles;
    const limit = parseInt(outputsize);

    if (start_date && !end_date) {
      // When only start_date is provided, get candles from start_date onwards in ascending order
      // but limit to outputsize and then reverse them to maintain newest-first in the response
      candles = await CandleData.find(query)
        .sort({ datetime: 1 }) // Ascending order (oldest first)
        .limit(limit)
        .lean();

      // Reverse the array to get newest-first order in the response
      candles.reverse();
    } else {
      // In all other cases, use descending order (newest first)
      candles = await CandleData.find(query)
        .sort({ datetime: -1 }) // Descending order (newest first)
        .limit(limit)
        .lean();
    }

    if (!candles || candles.length === 0) {
      throw new Error(`No data found for symbol: ${symbol}, interval: ${interval}`);
    }

    // Format response to match TwelveData API
    const response = {
      meta: {
        symbol: metaInfo.symbol,
        interval: interval,
        currency_base: metaInfo.currency_base,
        currency_quote: metaInfo.currency_quote,
        exchange: metaInfo.exchange,
        type: metaInfo.type
      },
      values: candles.map(candle => ({
        datetime: moment(candle.datetime).tz(DEFAULT_TIMEZONE).format('YYYY-MM-DD HH:mm:00'),
        open: candle.open,
        high: candle.high,
        low: candle.low,
        close: candle.close,
        ...(candle.volume ? { volume: candle.volume } : {})
      })),
      status: 'ok'
    };

    return response;
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error in getTimeSeries:`, error.message);
    throw error;
  }
};

module.exports = {
  saveCandleData,
  fetchAndStoreCandles,
  getTimeSeries
};
