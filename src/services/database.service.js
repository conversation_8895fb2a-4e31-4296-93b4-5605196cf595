/**
 * Database Service
 * Handles MongoDB connection and operations
 */
const mongoose = require('mongoose');
const config = require('../../config');

// Create a separate connection for the analysis database
const analysisConnection = mongoose.createConnection();

// Set mongoose debug mode based on configuration
mongoose.set('debug', config.mongodb.debug);

/**
 * Connect to MongoDB
 * @returns {Promise} Mongoose connection promise
 */
const connect = async () => {
  try {
    console.log(`[${new Date().toISOString()}] Connecting to MongoDB...`);
    await mongoose.connect(config.mongodb.uri, config.mongodb.options);
    console.log(`[${new Date().toISOString()}] Connected to MongoDB successfully`);

    // Connect to the analysis database
    console.log(`[${new Date().toISOString()}] Connecting to Analysis Database (${config.analysisDb.dbName})...`);
    await analysisConnection.openUri(config.analysisDb.uri, {
      ...config.mongodb.options,
      dbName: config.analysisDb.dbName // Explicitly set the database name
    });
    console.log(`[${new Date().toISOString()}] Connected to Analysis Database (${config.analysisDb.dbName}) successfully`);

    return mongoose.connection;
  } catch (error) {
    console.error(`[${new Date().toISOString()}] MongoDB connection error:`, error.message);
    throw error;
  }
};

/**
 * Disconnect from MongoDB
 * @returns {Promise} Mongoose disconnection promise
 */
const disconnect = async () => {
  try {
    console.log(`[${new Date().toISOString()}] Disconnecting from MongoDB...`);
    await mongoose.disconnect();
    console.log(`[${new Date().toISOString()}] Disconnected from MongoDB successfully`);

    // Disconnect from the analysis database
    console.log(`[${new Date().toISOString()}] Disconnecting from Analysis Database...`);
    await analysisConnection.close();
    console.log(`[${new Date().toISOString()}] Disconnected from Analysis Database successfully`);
  } catch (error) {
    console.error(`[${new Date().toISOString()}] MongoDB disconnection error:`, error.message);
    throw error;
  }
};

module.exports = {
  connect,
  disconnect,
  connection: mongoose.connection,
  analysisConnection
};
