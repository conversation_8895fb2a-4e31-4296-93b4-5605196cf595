const axios = require('axios');

/**
 * Fetch Delta Exchange option chain for a given underlying asset.
 * @param {string} underlyingSymbol - Example: 'BTCUSD' or 'ETHUSD'
 * @returns {Promise<Object>} - Option chain data as JSON
 */
async function getDeltaOption<PERSON>hain(underlyingSymbol, expiryDate) {
    try {
        const response = await axios.get('https://api.india.delta.exchange/v2/tickers', {
            params: {
                underlying_asset_symbols: underlyingSymbol,
                contract_types: 'call_options,put_options',
                expiry_date: expiryDate
            },
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (response.status === 200) {
            console.log('Option chain fetched successfully');
            return response.data;
        } else {
            console.error('Error fetching option chain:', response.status, response.statusText);
            return null;
        }
    } catch (error) {
        console.error('Error in getDeltaOptionChain:', error.response ? error.response.data : error.message);
        return null;
    }
}

module.exports = {
    getDeltaOptionChain
};
