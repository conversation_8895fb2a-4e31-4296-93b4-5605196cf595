/**
 * TwelveData Service
 * Handles interactions with the TwelveData API
 */
const axios = require('axios');
const config = require('../../config');

// Constants
const API_BASE_URL = 'https://api.twelvedata.com';
const DEFAULT_TIMEZONE = config.timezone.default;
const API_KEY = config.api.twelveDataApiKey;

/**
 * Fetch time series data from TwelveData API
 * @param {Object} params - Parameters for the API request
 * @returns {Promise<Object>} - API response data
 */
const fetchTimeSeries = async (params) => {
  try {
    const { symbol, interval, start_date, end_date, outputsize = 5000 } = params;
    
    if (!symbol || !interval) {
      throw new Error('Symbol and interval are required');
    }

    console.log(`[${new Date().toISOString()}] Fetching time series for ${symbol} (${interval})`);

    // Prepare request parameters
    const requestParams = {
      symbol,
      interval,
      apikey: API_KEY,
      outputsize,
      timezone: DEFAULT_TIMEZONE
    };

    // Add optional parameters if provided
    if (start_date) requestParams.start_date = start_date;
    if (end_date) requestParams.end_date = end_date;

    // Make API request
    const response = await axios.get(`${API_BASE_URL}/time_series`, {
      params: requestParams,
      headers: { 'Accept': 'application/json' },
      timeout: 30000 // 30 second timeout
    });

    // Validate response
    if (!response.data || response.data.status === 'error') {
      throw new Error(response.data?.message || 'Invalid response from TwelveData API');
    }

    if (!response.data.values || !response.data.values.length) {
      throw new Error('No data returned from TwelveData API');
    }

    // Log data range
    const startDate = response.data.values[response.data.values.length - 1].datetime;
    const endDate = response.data.values[0].datetime;
    console.log(`[${new Date().toISOString()}] Data received from ${startDate} to ${endDate}`);

    return response.data;
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error fetching time series:`, error.message);
    
    // Handle API-specific errors
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data;
      
      if (status === 429) {
        throw new Error('TwelveData API rate limit exceeded. Please try again later.');
      } else if (data && data.status === 'error') {
        throw new Error(`TwelveData API error: ${data.message}`);
      }
    }
    
    throw error;
  }
};

module.exports = {
  fetchTimeSeries
};
