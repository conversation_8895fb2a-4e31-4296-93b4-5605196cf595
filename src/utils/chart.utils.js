/**
 * Data utilities
 * Functions for data processing and analysis
 */

/**
 * Get the color of a candle (green for bullish, red for bearish)
 * @param {Object} candle - Candle data with open and close prices
 * @returns {string} Candle color ('green' or 'red')
 */
const getCandleColor = (candle) => {
  return candle.close > candle.open ? 'green' : 'red';
};

/**
 * Find valid sequences in data that match a reference sequence
 * @param {Array} d1 - Full data array
 * @param {Array} l1 - Reference sequence
 * @param {number} roundCoefficent - Coefficient for rounding
 * @param {string} compare - Comparison mode ('true' or 'false')
 * @returns {Array} Array of indices where valid sequences start
 */
const findValidSequencesV2 = (d1, l1, roundCoefficent, compare) => {
  const validSequences = [];
  
  // Early return if reference sequence is empty
  if (!l1 || l1.length === 0) {
    return validSequences;
  }
  
  for (let i = 0; i <= d1.length - l1.length; i++) {
    let previousDiffD1 = 0;
    let previousDiffL1 = 0;
    let isValid = true;
    
    for (let j = 0; j < l1.length; j++) {
      // Check if candle colors match
      if(!(getCandleColor(d1[i + j]) === getCandleColor(l1[j]))) {
        isValid = false;
        break;
      }

      const d1Value = d1[i + j].action;
      const l1Value = l1[j].action;
      
      // Validate data
      if (d1Value < 1) {
        isValid = false;
        break;
      }
      
      const div1 = Math.round(d1Value / l1Value);
      const div2 = Math.round(l1Value / d1Value);

      // Check price movement direction
      if(!(Math.sign(previousDiffD1 - d1Value) === Math.sign(previousDiffL1 - l1Value))) {
        previousDiffD1 = d1Value;
        previousDiffL1 = l1Value;
        isValid = false;
        break;
      }

      // Check if values are within acceptable range
      if (div1 !== d1Value / l1Value && div2 !== l1Value / d1Value) {
        const roundDiv1 = Math.abs(
          Math.round(d1Value / l1Value) - d1Value / l1Value,
        );
        const roundDiv2 = Math.abs(
          Math.round(l1Value / d1Value) - l1Value / d1Value,
        );
        
        if ((roundDiv1 > roundCoefficent) && compare === "true"){
          isValid = false;
          break;
        }

        if ((roundDiv2 > roundCoefficent) && compare === "false") {
          isValid = false;
          break;
        }

        previousDiffD1 = d1Value;
        previousDiffL1 = l1Value;
      }
    }
    
    if (isValid) {
      validSequences.push(i);
    }
  }
  
  return validSequences;
};

/**
 * Find and filter data based on area relative value difference
 * @param {Object} result - Data object with chart datasets
 * @param {number} relativeDifferenceInput - Maximum allowed relative difference (%)
 * @returns {Object} Filtered data
 */
const findAreaRelativeValueDifference = (result, relativeDifferenceInput = 3) => {
  // Early return if result is empty
  if (!result || Object.keys(result).length === 0) {
    return {};
  }
  
  // Function to normalize data points using min-max normalization
  function normalizeData(data) {
    const min = Math.min(...data.map(y => parseFloat(y)));
    const max = Math.max(...data.map(y => parseFloat(y)));
    
    return data.map(y => (parseFloat(y) - min) / (max - min));
  }

  // Function to calculate AUC using the trapezoidal rule on normalized data
  function calculateAUC(labels, data) {
    let auc = 0;
    
    for (let i = 1; i < labels.length; i++) {
      const x1 = new Date(labels[i - 1]).getTime();
      const x2 = new Date(labels[i]).getTime();
      const y1 = data[i - 1];
      const y2 = data[i];

      // Trapezoidal area between two points (x1, y1) and (x2, y2)
      const trapezoidArea = ((y1 + y2) / 2) * (x2 - x1);
      auc += trapezoidArea;
    }
    
    return auc;
  }

  // Function to remove datasets where relative difference is more than specified percentage
  function removeLargeDifferenceData(data, relativeDifferenceInput) {
    const referenceKey = "0";
    const referenceData = data[referenceKey];
    
    // Early return if reference data is missing
    if (!referenceData || !referenceData.datasets || !referenceData.datasets[0] || !referenceData.datasets[0].data) {
      return data;
    }
    
    const referenceNormalizedData = normalizeData(referenceData.datasets[0].data);
    const referenceAUC = calculateAUC(referenceData.labels, referenceNormalizedData);

    const filteredData = {};

    // Retain reference dataset
    filteredData[referenceKey] = data[referenceKey];

    Object.keys(data).forEach((key) => {
      if (key !== referenceKey) {
        const dataset = data[key];
        
        // Skip invalid datasets
        if (!dataset || !dataset.datasets || !dataset.datasets[0] || !dataset.datasets[0].data) {
          return;
        }
        
        const normalizedData = normalizeData(dataset.datasets[0].data);
        const currentAUC = calculateAUC(dataset.labels, normalizedData);
        
        // Calculate relative difference in %
        const relativeDifference = ((currentAUC - referenceAUC) / referenceAUC) * 100;

        // Only include datasets with <= specified % relative difference
        if (Math.abs(relativeDifference) <= relativeDifferenceInput) {
          filteredData[key] = data[key];
        }
      }
    });

    return filteredData;
  }

  return removeLargeDifferenceData(result, relativeDifferenceInput);
};

module.exports = {
  getCandleColor,
  findValidSequencesV2,
  findAreaRelativeValueDifference
};
