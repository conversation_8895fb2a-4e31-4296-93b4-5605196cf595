function findBestWorstGammaOTM(optionChainResponse, deltaRange = [0.10, 0.35], ivRange = [0.2, 0.6]) {
    if (!optionChainResponse.success || !Array.isArray(optionChainResponse.result)) {
        console.error('Invalid response structure');
        return null;
    }

    const deepOTMOptions = optionChainResponse.result.filter(option => {
        if (!option.greeks || !option.quotes) return false;
        const gamma = parseFloat(option.greeks.gamma);
        const delta = Math.abs(parseFloat(option.greeks.delta));
        const iv = parseFloat(option.quotes.ask_iv);
        const spot = parseFloat(option.spot_price);
        const strike = parseFloat(option.strike_price);
        const otm = Math.abs(strike - spot) / spot > 0.01; // for 1% OTM values

        return otm && gamma > 0 && delta >= deltaRange[0] && delta <= deltaRange[1] && iv >= ivRange[0] && iv <= ivRange[1];
    });

    if (deepOTMOptions.length === 0) {
        console.log('No deep OTM options found matching the filtering criteria.');
        return null;
    }

    let bestGammaOption = deepOTMOptions[0];
    let worstGammaOption = deepOTMOptions[0];

    deepOTMOptions.forEach(option => {
        const gamma = parseFloat(option.greeks.gamma);
        const bestGamma = parseFloat(bestGammaOption.greeks.gamma);
        const worstGamma = parseFloat(worstGammaOption.greeks.gamma);

        if (gamma > bestGamma) {
            bestGammaOption = option;
        }
        if (gamma < worstGamma) {
            worstGammaOption = option;
        }
    });

    return {
        bestGammaPerformer: {
            gamma: parseFloat(bestGammaOption.greeks.gamma),
            delta: parseFloat(bestGammaOption.greeks.delta),
            iv: parseFloat(bestGammaOption.quotes.ask_iv),
            spot_price: parseFloat(bestGammaOption.spot_price),
            strike_price: parseFloat(bestGammaOption.strike_price),
            symbol: bestGammaOption.symbol,
            mark_price: parseFloat(bestGammaOption.mark_price)
        },
        worstGammaPerformer: {
            gamma: parseFloat(worstGammaOption.greeks.gamma),
            delta: parseFloat(worstGammaOption.greeks.delta),
            iv: parseFloat(worstGammaOption.quotes.ask_iv),
            spot_price: parseFloat(worstGammaOption.spot_price),
            strike_price: parseFloat(worstGammaOption.strike_price),
            symbol: worstGammaOption.symbol,
            mark_price: parseFloat(worstGammaOption.mark_price)
        }
    };
}

module.exports = {
    findBestWorstGammaOTM
};
