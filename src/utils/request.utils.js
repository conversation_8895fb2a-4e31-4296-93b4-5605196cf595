/**
 * Request utilities
 * Functions for handling API requests
 */
const config = require('../../config');

/**
 * Execute multiple requests in parallel with concurrency control
 * @param {Array<Function>} requestFunctions - Array of async functions that return promises
 * @param {number} maxConcurrent - Maximum number of concurrent requests (default: from config)
 * @returns {Promise<Array>} - Results from all requests in the same order
 */
const executeParallelRequests = async (requestFunctions, maxConcurrent = config.concurrency.maxParallelRequests) => {
  // If no request functions, return empty array
  if (!requestFunctions || requestFunctions.length === 0) {
    return [];
  }

  // If only one request, execute it directly
  if (requestFunctions.length === 1) {
    return [await requestFunctions[0]()];
  }

  console.log(`[${new Date().toISOString()}] Executing ${requestFunctions.length} requests with max concurrency of ${maxConcurrent}`);
  
  // Create a copy of the request functions array
  const queue = [...requestFunctions];
  const results = new Array(requestFunctions.length);
  
  // Execute requests in batches
  while (queue.length > 0) {
    const batch = queue.splice(0, Math.min(maxConcurrent, queue.length));
    const batchIndices = batch.map((_, i) => requestFunctions.indexOf(batch[i]));
    
    console.log(`[${new Date().toISOString()}] Processing batch of ${batch.length} requests`);
    
    // Execute batch in parallel
    const batchResults = await Promise.all(batch.map(fn => fn().catch(error => {
      console.error(`[${new Date().toISOString()}] Error in parallel request:`, error.message);
      throw error; // Re-throw to be handled by the caller
    })));
    
    // Store results in the correct order
    batchIndices.forEach((originalIndex, batchIndex) => {
      results[originalIndex] = batchResults[batchIndex];
    });
  }
  
  return results;
};

module.exports = {
  executeParallelRequests
};
