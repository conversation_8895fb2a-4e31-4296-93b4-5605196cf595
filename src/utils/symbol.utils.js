/**
 * Pair utilities
 * Handles symbol mapping for trading pairs
 */

/**
 * Symbol mapping for trading pairs
 */
const symbolMap = {
  'BTC': 'BTC/USD',
  'JPY': 'USD/JPY',
  'EUR': 'EUR/USD',
  'AAPL': 'AAPL:NASDAQ',
  'AUD': 'USD/AUD',
  'Gold': 'XAU/USD'
};

/**
 * Get the trading symbol for a given code
 * @param {string} code - Symbol code
 * @returns {string} Trading symbol
 */
const getSymbol = (code) => {
  return symbolMap[code] || code;
};

module.exports = {
  getSymbol
};
